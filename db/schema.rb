# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_05_27_101922) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "application_templates", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "rules"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "audit_logs", force: :cascade do |t|
    t.string "action", null: false
    t.bigint "user_id"
    t.bigint "record_id"
    t.string "record_type"
    t.text "payload"
    t.text "request"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["action"], name: "index_audit_logs_on_action"
    t.index ["record_type", "record_id"], name: "index_audit_logs_on_record_type_and_record_id"
    t.index ["user_id", "action"], name: "index_audit_logs_on_user_id_and_action"
  end

  create_table "audits", force: :cascade do |t|
    t.integer "auditable_id"
    t.string "auditable_type"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.string "action"
    t.text "audited_changes"
    t.integer "version", default: 0
    t.string "comment"
    t.string "remote_address"
    t.string "request_uuid"
    t.datetime "created_at"
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id", "version"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "banners", force: :cascade do |t|
    t.string "image_src"
    t.string "position"
    t.string "html_content"
    t.string "hyperlink"
  end

  create_table "beacon_messages", force: :cascade do |t|
    t.string "name"
    t.string "help_scout_id"
    t.boolean "new_feature_message"
    t.string "domain_name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "billing_plans", force: :cascade do |t|
    t.string "name"
    t.string "short_name"
    t.decimal "price"
    t.integer "warning"
    t.integer "threshold"
    t.boolean "default"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "matches_shopify_plan"
    t.string "plan_type"
    t.text "features", default: [], array: true
    t.boolean "recommended", default: false
    t.boolean "development_plan", default: false
    t.boolean "available_for_development_shop", default: false
    t.boolean "available_for_production_shop", default: true
    t.string "interval"
    t.boolean "is_legacy", default: false, null: false
    t.string "currency", default: "USD", null: false
    t.index ["is_legacy"], name: "index_billing_plans_on_is_legacy"
    t.index ["short_name"], name: "index_billing_plans_on_short_name"
  end

  create_table "charges", force: :cascade do |t|
    t.string "shopify_id", null: false
    t.bigint "billing_plan_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["billing_plan_id"], name: "index_charges_on_billing_plan_id"
    t.index ["shopify_id"], name: "index_charges_on_shopify_id", unique: true
  end

  create_table "coupon_codes", force: :cascade do |t|
    t.string "coupon_code"
    t.boolean "redeemed", default: false
    t.integer "shop_id"
    t.integer "free_days", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "validity", precision: nil
    t.string "type"
    t.integer "redeem_counter", default: 1
  end

  create_table "cross_promotion_app_banners", force: :cascade do |t|
    t.string "name"
    t.string "link"
    t.boolean "active", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "title_de"
    t.string "title_en"
    t.string "text_de"
    t.string "text_en"
    t.string "link_text_de"
    t.string "link_text_en"
    t.string "cta_text_de"
    t.string "cta_text_en"
    t.string "cta_url"
    t.string "video_id"
    t.integer "video_length"
    t.index ["name"], name: "index_cross_promotion_app_banners_on_name", unique: true
  end

  create_table "error_logs", force: :cascade do |t|
    t.bigint "shop_id"
    t.string "exception_type"
    t.string "error_info_internal"
    t.string "error_info_external"
    t.string "shopify_id"
    t.string "shopify_name"
    t.bigint "error_type_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "shopify_type"
    t.json "retry_params"
    t.bigint "order_id"
    t.index ["error_type_id"], name: "index_error_logs_on_error_type_id"
    t.index ["order_id"], name: "index_error_logs_on_order_id"
    t.index ["shop_id", "created_at"], name: "index_error_logs_on_shop_id_and_created_at"
    t.index ["shop_id", "id"], name: "index_error_logs_on_shop_id_and_id"
    t.index ["shop_id", "order_id"], name: "index_error_logs_on_shop_id_and_order_id"
    t.index ["shop_id"], name: "index_error_logs_on_shop_id"
    t.index ["shopify_id"], name: "index_error_logs_on_shopify_id"
  end

  create_table "error_types", force: :cascade do |t|
    t.string "signal"
    t.string "external_info_text_key"
    t.string "helpscout_article_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "job_statuses", id: false, force: :cascade do |t|
    t.string "guid", null: false
    t.string "message"
    t.decimal "percent"
    t.string "stage"
    t.boolean "running"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "shop_id"
    t.integer "total_job_count", default: 0
    t.integer "current_job_count", default: 0
    t.string "lock_version"
    t.integer "invoices_queued", default: 0
    t.integer "invoices_processed", default: 0
    t.integer "invoices_skipped", default: 0
    t.integer "refunds_queued", default: 0
    t.integer "refunds_processed", default: 0
    t.integer "refunds_skipped", default: 0
    t.integer "transactions_queued", default: 0
    t.integer "transactions_processed", default: 0
    t.integer "transactions_skipped", default: 0
    t.index ["guid"], name: "index_job_statuses_on_guid", unique: true
    t.index ["shop_id"], name: "index_job_statuses_on_shop_id"
  end

  create_table "service_instances", force: :cascade do |t|
    t.string "name"
    t.string "css_class"
    t.string "logo_1"
    t.string "logo_2"
    t.string "url"
    t.string "portal_url"
    t.string "website_url"
    t.string "module"
  end

  create_table "sessions", force: :cascade do |t|
    t.string "session_id", null: false
    t.text "data"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["session_id"], name: "index_sessions_on_session_id", unique: true
    t.index ["updated_at"], name: "index_sessions_on_updated_at"
  end

  create_table "shop_settings", force: :cascade do |t|
    t.integer "shop_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "service_api_token"
    t.boolean "create_orders"
    t.boolean "create_invoices", default: true
    t.boolean "send_invoice_mail"
    t.string "invoice_mail_subject", default: "Ihre Rechnung zu Auftrag {{name}}"
    t.string "invoice_mail_body", default: "Vielen Dank für Ihren Einkauf. Sie finden die Rechnung im Anhang dieser Mail im PDF Format. Wir hoffen, Sie bald wieder als Kunden begrüßen zu dürfen."
    t.string "invoice_account"
    t.boolean "excludePOS", default: true
    t.boolean "create_refunds", default: true
    t.string "invoice_pretext", default: "Hallo {{customer.first_name}} {{customer.last_name}}, sie erhalten heute Ihre Rechnung über die folgenden Positionen zu Auftrag {{name}}."
    t.string "invoice_posttext", default: ""
    t.boolean "mark_due_immediately", default: true
    t.string "invoice_title", default: "Rechnung zu {{name}}"
    t.boolean "create_customer", default: false
    t.string "refund_pretext", default: ""
    t.string "refund_posttext", default: ""
    t.string "refund_title", default: "Gutschrift zu {{name}}"
    t.string "credit_note_mail_body", default: ""
    t.string "credit_note_mail_subject", default: ""
    t.string "invoice_language", default: "de"
    t.integer "shipping_min_days"
    t.integer "shipping_max_days"
    t.string "shipping_type"
    t.boolean "enable_tender_transactions", default: false
    t.boolean "enable_tah_creation", default: false
    t.boolean "calculate_shipping_tax", default: false
    t.string "invoice_timing", default: "orders/fulfilled"
    t.integer "invoice_mail_offset_days", default: 0
    t.boolean "use_SKUs", default: false
    t.boolean "confirm_tax_settings", default: false
    t.string "order_exclusion_tags"
    t.text "mail_exclusion_tags", default: ""
    t.string "invoice_layout_id", default: ""
    t.string "refund_layout_id", default: ""
    t.boolean "use_shipping_address_for_invoices", default: false
    t.index ["shop_id"], name: "index_shop_settings_on_shop_id"
  end

  create_table "shops", force: :cascade do |t|
    t.string "shopify_domain", null: false
    t.string "shopify_token", null: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "email"
    t.string "name"
    t.string "country"
    t.string "shop_owner"
    t.decimal "discount_percent", default: "0.0"
    t.boolean "nohelp"
    t.integer "service_instance_id", default: 1
    t.string "lexoffice_token"
    t.string "lexoffice_refresh_token"
    t.integer "lexoffice_token_expires_at"
    t.boolean "credit_note_scope"
    t.boolean "send_mail_scope"
    t.string "lexoffice_organization_id"
    t.string "lexoffice_tax_type"
    t.boolean "lexoffice_small_business"
    t.string "lexoffice_connection_id"
    t.datetime "connection_established_at", precision: nil
    t.boolean "connection_needs_auth_refresh"
    t.datetime "last_error_mail_sent", precision: nil
    t.string "distance_sales_principle", default: "not defined"
    t.integer "import_discount_percent", default: 0
    t.boolean "finance_scope"
    t.integer "finance_account_id"
    t.integer "coupon_code_id"
    t.bigint "redeemed_coupon_id"
    t.date "trial_ends_on"
    t.boolean "invoice_info_job_ran", default: false
    t.datetime "plan_mismatch_since"
    t.boolean "has_multiple_tax_lines"
    t.string "access_scopes", default: "", null: false
    t.boolean "tmp_has_missing_recurring_charge", default: false, null: false
    t.bigint "legacy_billing_plan_id"
    t.boolean "legacy_billing_activated", default: false, null: false
    t.boolean "legacy_plan_needs_update", default: false, null: false
    t.boolean "legacy_import_unlocked", default: false, null: false
    t.datetime "import_manually_unlocked_at"
    t.boolean "internal_test_shop", default: false, null: false
    t.datetime "import_unlocked_at"
    t.datetime "uninstalled_at"
    t.index ["lexoffice_organization_id"], name: "index_shops_on_lexoffice_organization_id"
    t.index ["service_instance_id"], name: "index_shops_on_service_instance_id"
    t.index ["uninstalled_at"], name: "index_shops_on_uninstalled_at"
  end

  create_table "snippets", force: :cascade do |t|
    t.string "name"
    t.text "code"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "support_user_events", force: :cascade do |t|
    t.bigint "support_user_id", null: false
    t.string "name", null: false
    t.jsonb "properties", default: {}, null: false
    t.datetime "time", null: false
    t.index ["name", "time"], name: "index_support_user_events_on_name_and_time"
    t.index ["support_user_id"], name: "index_support_user_events_on_support_user_id"
  end

  create_table "support_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "provider"
    t.string "uid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_support_users_on_email", unique: true
  end

  create_table "sync_infos", force: :cascade do |t|
    t.integer "shop_id"
    t.string "shopify_id"
    t.string "target_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "sync_rule_id"
    t.string "target_type"
    t.string "last_action"
    t.boolean "invoice_mail_sent"
    t.boolean "import"
    t.string "shopify_order_name"
    t.datetime "shopify_created_at", precision: nil
    t.text "extra_infos"
    t.bigint "shopify_order_id"
    t.index ["import"], name: "index_sync_infos_on_import"
    t.index ["last_action"], name: "idx_sync_infos_last_action"
    t.index ["shop_id", "created_at"], name: "index_sync_infos_on_shop_id_and_created_at"
    t.index ["shop_id", "shopify_created_at"], name: "index_sync_infos_on_shop_id_and_shopify_created_at"
    t.index ["shop_id", "shopify_order_id"], name: "index_sync_infos_on_shop_id_and_shopify_order_id"
    t.index ["shop_id"], name: "index_sync_infos_on_shop_id"
    t.index ["shopify_id"], name: "index_sync_infos_on_shopify_id"
    t.index ["shopify_order_id", "target_type"], name: "index_sync_infos_on_shopify_order_id_and_target_type"
    t.index ["shopify_order_id"], name: "index_sync_infos_on_shopify_order_id"
    t.index ["shopify_order_name"], name: "idx_sync_infos_order_name"
    t.index ["sync_rule_id"], name: "index_sync_infos_on_sync_rule_id"
    t.index ["target_id"], name: "index_sync_infos_on_target_id"
    t.index ["target_type"], name: "idx_sync_infos_target_type"
  end

  create_table "sync_rule_templates", force: :cascade do |t|
    t.string "name"
    t.string "filters"
    t.string "shopify_entity_type"
    t.string "trello_list"
    t.boolean "live_sync"
    t.boolean "use_images"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["name"], name: "index_sync_rule_templates_on_name", unique: true
  end

  create_table "sync_rules", force: :cascade do |t|
    t.string "shopify_entity_type"
    t.boolean "live_sync"
    t.boolean "sync_back"
    t.boolean "use_images"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "shop_id"
    t.string "webhooks"
    t.string "target_type"
    t.string "target_action"
    t.index ["shop_id"], name: "index_sync_rules_on_shop_id"
  end

  create_table "transaction_settings", force: :cascade do |t|
    t.boolean "enable_amazon", default: false
    t.string "amazon_account_id"
    t.boolean "enable_apple_pay", default: false
    t.string "apple_pay_account_id"
    t.boolean "enable_credit_card", default: false
    t.string "credit_card_account_id"
    t.boolean "enable_google_pay", default: false
    t.string "google_pay_account_id"
    t.boolean "enable_klarna", default: false
    t.string "klarna_account_id"
    t.boolean "enable_samsung_pay", default: false
    t.string "samsung_pay_account_id"
    t.boolean "enable_shopify_pay", default: false
    t.string "shopify_pay_account_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "shop_id"
    t.boolean "enable_klarna_pay_later", default: false
    t.string "klarna_pay_later_account_id"
    t.boolean "enable_sofort", default: false
    t.string "sofort_account_id"
    t.boolean "enable_other", default: false
    t.string "other_account_id"
    t.text "extra_accounts_info"
    t.index ["shop_id"], name: "index_transaction_settings_on_shop_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "charges", "billing_plans"
  add_foreign_key "error_logs", "error_types"
  add_foreign_key "error_logs", "shops"
  add_foreign_key "job_statuses", "shops"
  add_foreign_key "support_user_events", "support_users"
  add_foreign_key "transaction_settings", "shops"
end
