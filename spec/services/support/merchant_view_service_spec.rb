# frozen_string_literal: true

require "rails_helper"

RSpec.describe Support::MerchantViewService do
  let(:shop) { create(:shop) }
  let(:user) { create(:support_user) }
  let(:request) { double("request", params: { coupon_code: nil }) }
  let(:shop_setting) { create(:shop_setting, shop:) }
  let(:transaction_setting) { create(:transaction_setting, shop:) }
  let(:service) { described_class.new(shop, user, request) }

  before do
    allow(shop).to receive(:shop_setting).and_return(shop_setting)
    allow(shop).to receive(:transaction_setting).and_return(transaction_setting)
    allow(shop).to receive(:with_shopify_session).and_yield

    # Mock app_installation data
    app_installation = double("app_installation")
    active_subscriptions = double("active_subscriptions")
    one_time_purchases = double("one_time_purchases")
    edges = double("edges")
    node = double("node")

    allow(shop).to receive(:app_installation).and_return(app_installation)
    allow(app_installation).to receive(:activeSubscriptions).and_return(active_subscriptions)
    allow(app_installation).to receive(:oneTimePurchases).and_return(one_time_purchases)
    allow(active_subscriptions).to receive(:first).and_return(double("recurring_charge", id: "subscription_123"))
    allow(one_time_purchases).to receive(:edges).and_return(edges)
    allow(edges).to receive(:first).and_return(double("edge", node:))

    allow(ShopifyBilling::SelectAvailableBillingPlansService).to receive(:call).and_return([])
  end

  describe ".call" do
    it "creates an audit log entry" do
      expect(AuditLog).to receive(:audit!).with(
        :view_merchant,
        user:,
        request:,
        payload: { shop: shop.shopify_domain }
      )

      service.call
    end

    it "returns a hash with all required data" do
      result = service.call

      expect(result).to include(
        :shop_info,
        :shop_settings,
        :shopify_shop_info,
        :app_recurring_charges,
        :app_import_charges,
        :current_plan,
        :audits,
        :import_plan,
        :old_imports,
        :available_plans
      )
    end

    it "fetches billing data correctly" do
      charge = double("charge")
      billing_plan = double("billing_plan")

      allow(ShopifyBilling::Charge).to receive(:find_by).with(shopify_id: "subscription_123")
        .and_return(charge)
      allow(charge).to receive(:billing_plan).and_return(billing_plan)
      allow(ShopifyBilling::BillingPlan).to receive(:find_by).with(short_name: "import")
        .and_return(double("import_plan"))
      allow(JobStatus).to receive(:where).and_return(double("job_status", order: []))

      result = service.call

      expect(result[:current_plan]).to eq(billing_plan)
    end

    it "fetches Shopify data correctly" do
      result = service.call

      expect(result[:shopify_shop_info]).to eq(shop)
      expect(result[:app_recurring_charges]).to be_present
      expect(result[:app_import_charges]).to be_present
    end
  end
end
