import React, {useContext, useMemo, useEffect, useState} from "react";
import { useAuthenticatedFetch } from "@shopify/app-bridge-react";
import { useLocation, useNavigate } from "react-router-dom";
import {
    Card,
    Page,
    Layout,
    TextContainer,
    Image,
    Text,
    ButtonGroup,
    Button,
    Loading,
    Frame,
    SkeletonDisplayText,
    SkeletonBodyText,
    InlineStack,
    Icon, Box
} from "@shopify/polaris";
import { CircleTickMajor, DiamondAlertMajor } from "@shopify/polaris-icons";
import { useToast } from '@shopify/app-bridge-react';
import { useTranslation } from "react-i18next";
import { CrossPromotionBanner } from 'cross-promotion-app'

import { EshopGuideContext } from "../../shared_components/providers/EshopGuideProvider";
import HelpFooter from "../../shared_components/components/HelpFooter";
import DisconnectConfirmationModal from "../../shared_components/components/DisconnectConfirmationModal";
import FeedbackCard from "../../shared_components/components/FeedbackCard";
import PlanMismatchBanner from "../components/PlanMismatchBanner.jsx";
import {DismissibleBanner} from "./DismissibleBanner";

function Feedback() {
    return null;
}

export default function Overview({ reviewUrl, feedbackUrl, overviewImage, overviewImageSuccess, servicePath,
                                     disconnectServicePath }) {
    const { shopInfo, appService, refetchShopInfo } = useContext(EshopGuideContext);
    const navigate = useNavigate();
    const { t } = useTranslation();
    const { show } = useToast();

    useEffect(() => {
        if (shopInfo?.settings !== undefined){
            refetchShopInfo();
        }
    }, []);

    useEffect(() => {
        window.shop_domain = shopInfo?.shop_domain;
    }, [shopInfo]);

    const setupSuccessful =
        shopInfo &&
        shopInfo.billing?.plan_active &&
        shopInfo.service?.connected_to_service &&
        shopInfo.settings?.invoice_creation_enabled &&
        shopInfo.settings?.tax_settings_confirmed

    const fetch = useAuthenticatedFetch();

    {/* TODO: Move lexoffice specific logic to project files */}
    const connectionString = useMemo(() => {
        if (shopInfo?.service?.connected_to_service) {
            const params = {
                connectedSince: new Date(shopInfo.service?.connection_info?.connection_established_at),
                organization: shopInfo.service?.connection_info?.lexoffice_organization_name,
                username: shopInfo.service?.connection_info?.username,
                formatParams: {
                    connectedSince: {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                    },
                },
            };

            return shopInfo.service?.connection_info?.lexoffice_organization_name
                ? t("overview.service.connected.description_with_organization", params)
                : t("overview.service.connected.description", params);
        }

        return t("overview.service.not_connected.description");
    }, [shopInfo]);

    {/* Confirm modal for disconnect from service */}
    const [disconnectModalActive, setDisconnectModalActive] = useState(false);
    const showDisconnectModal = () => { setDisconnectModalActive(true) }

    {/* Show notification after redirection from coupon/plan activation */}
    const queryParams = new URLSearchParams(useLocation().search);
    const status = queryParams.get("status");
    const statusMessages = {
        plan_activated: () => show(t("billing.plan.notifications.success")),
        import_plan_activated: () => show(t("billing.plan.notifications.import_success")),
        coupon_activated: () => show(t("billing.coupon.notifications.success")),
        declined: () => show(t("billing.plan.notifications.declined"), { isError: true }),
        no_charge: () => show(t("billing.plan.notifications.no_charge"), { isError: true }),
    };

    return (
        <Frame>
            {!shopInfo && <Loading />}
            <Page>
                {statusMessages[status]?.()}
                <Layout>
                    {disconnectModalActive && <DisconnectConfirmationModal disconnectPath={disconnectServicePath}
                                                                           active={disconnectModalActive}
                                                                           setActive={setDisconnectModalActive}
                                                                           refetchShopInfo={refetchShopInfo}/>}

                    {/* status overview */}
                    <Layout.Section>
                        <Card padding="600">
                            <InlineStack wrap={false} distribution="trailing">
                                <div>
                                    <TextContainer spacing="loose">
                                        <Text as="h1" variant="heading3xl">
                                            {t("general.app_name")}
                                        </Text>
                                        <p dangerouslySetInnerHTML={{ __html: t("general.app_description") }} />
                                        {setupSuccessful && (
                                            <>
                                                <Text as="p" fontWeight="semibold" tone="success">
                                                    {t("overview.status.setup_success")}
                                                </Text>
                                                <ButtonGroup>
                                                    <Button  onClick={() => navigate("/ShopSettings")} variant="primary">
                                                        {t("overview.status.go_to_settings")}
                                                    </Button>
                                                    <Button onClick={() => navigate("/Help")}>
                                                        {t("overview.status.help")}
                                                    </Button>
                                                </ButtonGroup>
                                            </>
                                        )}
                                        {shopInfo && !setupSuccessful && (
                                            <>
                                                {!shopInfo.service?.connected_to_service && (
                                                    <>
                                                        <InlineStack gap="200" wrap={false} align="start">
                                                            <span><Icon source={DiamondAlertMajor} tone="critical" /></span>
                                                            <Text as="p" fontWeight="semibold" tone="critical">
                                                                {t("overview.service_name") + " " + t("overview.service.not_connected.status")}
                                                            </Text>
                                                        </InlineStack>
                                                        <Button  onClick={() => appService.connect(fetch)} variant="primary">
                                                            {t("overview.service.not_connected.connect_now")}
                                                        </Button>
                                                    </>
                                                )}
                                                {shopInfo.service?.connected_to_service && !shopInfo.billing?.plan_active && (
                                                    <>
                                                        <InlineStack gap="200" wrap={false} align="start">
                                                            <span><Icon source={DiamondAlertMajor} tone="critical" /></span>
                                                            <Text as="p" fontWeight="semibold" tone="critical">
                                                                {t("overview.plan.subscription_plan") + " " + t("overview.plan.not_activated")}
                                                            </Text>
                                                        </InlineStack>
                                                        <Button  onClick={() => navigate("/PlansAndCoupons")} variant="primary">
                                                            {t("overview.plan.go_to_plan")}
                                                        </Button>
                                                    </>
                                                )}
                                                {shopInfo.service?.connected_to_service && shopInfo.billing?.plan_active&& !shopInfo.settings?.invoice_creation_enabled && (
                                                    <>
                                                        <InlineStack gap="200" wrap={false} align="start">
                                                            <span><Icon source={DiamondAlertMajor} tone="critical" /></span>
                                                            <Text as="p" fontWeight="semibold" tone="critical">
                                                                {t("overview.settings.invoice_creation") + " " + t("overview.settings.not_activated")}
                                                            </Text>
                                                        </InlineStack>
                                                        <Button onClick={() => navigate("ShopSettings?tab=1")}>
                                                            {t("overview.settings.go_to_invoice_settings")}
                                                        </Button>
                                                    </>
                                                )}
                                                {shopInfo.service?.connected_to_service && shopInfo.billing?.plan_active&& shopInfo.settings?.invoice_creation_enabled && !shopInfo.settings?.tax_settings_confirmed && (
                                                    <>
                                                        <InlineStack gap="200" wrap={false} align="start">
                                                            <span><Icon source={DiamondAlertMajor} tone="critical" /></span>
                                                            <Text as="p" fontWeight="semibold" tone="critical">
                                                                {t("overview.tax_settings.title") + " " + t("overview.tax_settings.not_confirmed")}
                                                            </Text>
                                                        </InlineStack>
                                                        <Button  onClick={() => navigate("ShopSettings?tab=0")} variant="primary">
                                                            {t("overview.tax_settings.go_to_tax_settings")}
                                                        </Button>
                                                    </>
                                                )}
                                            </>
                                        )}
                                        {!shopInfo && (
                                            <>
                                                <SkeletonBodyText lines={2} />
                                                <SkeletonDisplayText size="large" />
                                            </>
                                        )}
                                    </TextContainer>
                                </div>
                                <div className="mobile--hidden" style={{ margin: "0 0 0 20px" }}>
                                    <Image source={setupSuccessful ? (overviewImageSuccess || overviewImage) : overviewImage} width={256} />
                                </div>
                            </InlineStack>
                        </Card>
                    </Layout.Section>

                    {/* overview status header */}
                    <Layout.Section>
                        <BlockStack gap="400">
                            <PlanMismatchBanner showAction />
                            <Box paddingInlineStart={{xs:"4", sm:"0"}}>
                                <Text variant="headingMd">
                                    {t("overview.status.heading")}
                                </Text>
                            </Box>
                        </BlockStack>
                    </Layout.Section>

                    {/* service integration status */}
                    <Layout.Section>
                        <Card>
                            {!shopInfo && <SkeletonBodyText lines={2} />}
                            {shopInfo && (
                                <InlineStack wrap="false" gap={{xs: "100", md: "200"}} align="space-between">
                                    <Icon
                                        source={CircleTickMajor}
                                        tone={shopInfo.service?.connected_to_service ? "success" : "subdued"}
                                    />
                                    <div style={{ flexGrow: 1 }}>
                                        <Text fontWeight="semibold">
                                            {t("overview.service_name")}{" "}
                                            {shopInfo.service?.connected_to_service && (
                                                <Text as="span" tone="success">
                                                    {t("overview.service.connected.status")}
                                                </Text>
                                            )}
                                            {!shopInfo.service?.connected_to_service && (
                                                <Text as="span" tone="critical">
                                                    {t("overview.service.not_connected.status")}
                                                </Text>
                                            )}
                                        </Text>
                                        {connectionString && <Text tone="subdued">{connectionString}</Text>}
                                    </div>
                                    {shopInfo.service?.connected_to_service && (
                                        <ButtonGroup>
                                            <Button

                                                onClick={() => window.open(servicePath, "_blank").focus()}
                                                variant="primary">
                                                {t("overview.service.go_to_service")}
                                            </Button>
                                            <Button onClick={() => showDisconnectModal()}>
                                                {t("overview.service.disconnect.action")}
                                            </Button>
                                        </ButtonGroup>
                                    )}
                                    {!shopInfo.service?.connected_to_service && (
                                        <Button onClick={() => appService.connect(fetch)}>
                                            {t("overview.service.not_connected.connect_now")}
                                        </Button>
                                    )}
                                </InlineStack>
                            )}
                        </Card>
                    </Layout.Section>

                    {/* billing plan status */}
                    <Layout.Section>
                        <Card>
                            {!shopInfo && <SkeletonBodyText lines={2} />}
                            {shopInfo && (
                                <InlineStack wrap="false" gap={{xs: "100", md: "200"}} align="space-between">
                                    <Icon
                                        source={CircleTickMajor}
                                        tone={shopInfo.billing?.plan_active ? "success" : "subdued"}
                                    />
                                    <div style={{ flexGrow: 1 }}>
                                        <Text fontWeight="semibold">
                                            {t("overview.plan.subscription_plan")}{" "}
                                            {shopInfo.billing?.plan_active && (
                                                <Text as="span" tone="success">
                                                    {t("overview.plan.activated")}
                                                </Text>
                                            )}
                                            {!shopInfo.billing?.plan_active && (
                                                <Text as="span" tone="critical">
                                                    {t("overview.plan.not_activated")}
                                                </Text>
                                            )}
                                        </Text>
                                        <Text tone="subdued">
                                            {shopInfo.billing?.plan_active
                                                ? t("overview.plan.activated_description", {plan: shopInfo.billing?.billing_plan_name,})
                                                : t("overview.plan.not_activated_description")}

                                            {shopInfo.billing?.plan_active && shopInfo.billing?.remaining_trial_days > 0 &&
                                                `. ` + t("overview.plan.activated_trial", {days: shopInfo.billing.remaining_trial_days})
                                            }
                                        </Text>
                                    </div>
                                    <Button onClick={() => navigate("/PlansAndCoupons")}>
                                        {t("overview.plan.go_to_plan")}
                                    </Button>
                                </InlineStack>
                            )}
                        </Card>
                    </Layout.Section>

                    {/* invoice settings status */}
                    <Layout.Section>
                        <Card>
                            {!shopInfo && <SkeletonBodyText lines={2} />}
                            {shopInfo && (
                                <InlineStack wrap="true" gap={{xs:"100", md:"200"}} align="space-between">
                                    <Icon
                                        source={CircleTickMajor}
                                        tone={shopInfo.settings?.invoice_creation_enabled ? "success" : "subdued"}
                                    />
                                    <div style={{ flexGrow: 1 }}>
                                        <Text fontWeight="semibold">
                                            {t("overview.settings.invoice_creation")}{" "}
                                            {shopInfo.settings?.invoice_creation_enabled && (
                                                <Text as="span" tone="success">
                                                    {t("overview.settings.activated")}
                                                </Text>
                                            )}
                                            {!shopInfo.settings?.invoice_creation_enabled && (
                                                <Text as="span" tone="critical">
                                                    {t("overview.settings.not_activated")}
                                                </Text>
                                            )}
                                        </Text>
                                        <Text tone="subdued">
                                            {shopInfo.settings?.invoice_creation_enabled
                                                ? t("overview.settings.invoice_creation_enabled_description")
                                                : t("overview.settings.invoice_creation_disabled_description")}
                                        </Text>
                                    </div>
                                    <Button onClick={() => navigate("ShopSettings?tab=1")}>
                                        {t("overview.settings.go_to_invoice_settings")}
                                    </Button>
                                </InlineStack>
                            )}
                        </Card>
                    </Layout.Section>

                    {/* confirm tax settings status */}
                    <Layout.Section>
                        <Card>
                            {!shopInfo && <SkeletonBodyText lines={2} />}
                            {shopInfo && (
                                <InlineStack wrap="true" gap={{xs:"100", md:"200"}} align="space-between">
                                    <Icon
                                        source={CircleTickMajor}
                                        tone={shopInfo.settings?.tax_settings_confirmed ? "success" : "subdued"}
                                    />
                                    <div style={{ flexGrow: 1 }}>
                                        <Text fontWeight="semibold">
                                            {t("overview.tax_settings.title")}{" "}
                                            {shopInfo.settings?.tax_settings_confirmed && (
                                                <Text as="span" tone="success">
                                                    {t("overview.tax_settings.confirmed")}
                                                </Text>
                                            )}
                                            {!shopInfo.settings?.tax_settings_confirmed && (
                                                <Text as="span" tone="critical">
                                                    {t("overview.tax_settings.not_confirmed")}
                                                </Text>
                                            )}
                                        </Text>
                                        <Text tone="subdued">
                                            {shopInfo.settings?.tax_settings_confirmed
                                                ? t("overview.tax_settings.description_enabled")
                                                : t("overview.tax_settings.description_disabled")}
                                        </Text>
                                    </div>
                                    <Button onClick={() => navigate("ShopSettings?tab=0")}>
                                        {t("overview.tax_settings.go_to_tax_settings")}
                                    </Button>
                                </InlineStack>
                            )}
                        </Card>
                    </Layout.Section>

                    {/* feedback banner */}
                    <Layout.Section>
                        <DismissibleBanner storageKey={"tax_hint"}
                                           title={t("overview.tax_banner.title")}
                                           description={t("overview.tax_banner.description")}
                                           children={
                                               <Button onClick={() => {window.Beacon ? window.Beacon("article", "401") : window.Beacon("navigate", "/ask/message")}}>
                                                   {t("overview.tax_banner.action")}
                                               </Button>
                                           }
                        />
                    </Layout.Section>

                    <CrossPromotionBanner />

                    {/* feedback bar */}
                    {setupSuccessful && (
                        <Layout.Section>
                            <FeedbackCard reviewUrl={reviewUrl} feedbackUrl={feedbackUrl}/>
                        </Layout.Section>
                    )}
                </Layout>
                <HelpFooter/>
            </Page>
        </Frame>
    );
}
