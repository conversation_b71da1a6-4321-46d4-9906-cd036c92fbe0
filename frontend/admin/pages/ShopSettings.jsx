import React, { useCallback, useContext, useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import GeneralSettings from "../components/Settings/GeneralSettings.jsx";
import InvoiceSettings from "../components/Settings/InvoiceSettings.jsx";
import CreditNoteSettings from "../components/Settings/CreditNoteSettings.jsx";
import PaymentDataSettings from "../components/Settings/PaymentDataSettings.jsx";
import MailingSettings from "../components/Settings/MailingSettings.jsx";
import { useAppQuery, useAuthenticatedFetch } from "~/hooks";
import { EshopGuideContext } from "../../shared_components/providers/EshopGuideProvider";
import { OrderDataProvider } from "../components/providers/OrderDataProvider";
import SaveBar from "../shared/components/SaveBar.jsx";
import PlanActivationWarnings from "../shared/components/PlanActivationWarnings.jsx";
import HelpFooter from "~/shared/components/HelpFooter";
import {
  Page,
  Divider,
  Loading,
  Frame,
  Tabs,
  InlineStack,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import {useLocale, useToast} from "@shopify/app-bridge-react";
import ViewModeSelection from "../components/Settings/ViewModeSelection.jsx";
import { ViewModeProvider } from "../components/providers/ViewModeProvider.jsx";


export default function ShopSettings() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const authenticatedFetch = useAuthenticatedFetch();
  const { shopInfo } = useContext(EshopGuideContext);

  const [shopSettings, setShopSettings] = useState({});
  const [transactionSettings, setTransactionSettings] = useState({});
  const [beaconMessages, setBeaconMessages] = useState([]);
  const domainNames = [
    "general-settings",
    "invoice",
    "credit-note",
    "payments",
  ];
  const domainsMessages = {};

  domainNames.forEach((domain) => {
    domainsMessages[domain] = beaconMessages.filter(
      (msg) => msg.domain_name === domain
    );
  });

  const queryParams = new URLSearchParams(useLocation().search);
  const tabIndex = parseInt(queryParams.get("tab"));
  const [selected, setSelected] = useState(
    tabIndex >= 0 && tabIndex <= 3 ? tabIndex : 0
  );
  const handleTabChange = useCallback((selectedTabIndex) => {
    setSelected(selectedTabIndex);
    refetchSettings();
  }, []);

  const [saveBarVisible, setSaveBarVisible] = useState(false);

  const {
    data: shopSettingsData,
    isLoading: isLoadingSettings,
    refetch: refetchSettings,
  } = useAppQuery({ url: "/api/shop_settings" });

  const { data: beaconMessagesData } = useAppQuery({
    url: "/api/beacon_messages",
  });

  const ensureCorrectMinMaxDays = (settings) => {
    if (settings.shop_setting.shipping_max_days === null) {
      settings.shop_setting.shipping_max_days = 0;
    }
    if (settings.shop_setting.shipping_min_days === null) {
      settings.shop_setting.shipping_min_days = 0;
    }
  };

  useEffect(() => {
    if (shopSettingsData && beaconMessagesData) {
      ensureCorrectMinMaxDays(shopSettingsData);
      setShopSettings(shopSettingsData.shop_setting);
      setTransactionSettings(shopSettingsData.transaction_setting);
      setBeaconMessages(beaconMessagesData.beacon_messages);
    }
  }, [shopSettingsData, beaconMessagesData]);

  const paymentSettingsHandler = (
    settings,
    value,
    propName,
    isExtraTransactionSetting
  ) => {
    if (isExtraTransactionSetting) {
      return {
        ...settings,
        ["extra_accounts_info"]: {
          ...settings.extra_accounts_info,
          [propName]: value,
        },
      };
    } else {
      return {
        ...settings,
        [propName]: value,
      };
    }
  };

  const handleSettingsChange = useCallback(
    (
      value,
      propName,
      isTransactionSetting = false,
      isExtraTransactionSetting = false
    ) => {
      if (isTransactionSetting === true) {
        setTransactionSettings((prevSettings) =>
          paymentSettingsHandler(
            prevSettings,
            value,
            propName,
            isExtraTransactionSetting
          )
        );
      } else {
        setShopSettings((prevSettings) => ({
          ...prevSettings,
          [propName]: value,
        }));
      }
      setSaveBarVisible(true);
    },
    []
  );

  const { show } = useToast();
  const locale = useLocale();
  const saveChanges = async (
    settings,
    transactions,
    successCallback = () => {},
    errorCallback = () => {}
  ) => {
    const response = await authenticatedFetch("/api/shop_settings", {
      method: "POST",
      body: JSON.stringify({
        shop_setting: settings,
        transaction_setting: transactions,
      })
    });

    const data = await response.json();

    if (response.ok && data.success) {
      await refetchSettings();
      show(t("settings.notifications.success"));
      successCallback();
      return;
    }

    // Handle different types of errors
    if (data.error_type === 'validation_error' && data.errors && data.errors.length > 0) {
      // Get the first error
      const error = data.errors[0];

      // Standardized approach: directly map error.key to validation.${error.key}
      const translationKey = `validation.${error.key}`;

      // Prepare translation options with field label and all error options
      const translationOptions = {
        label: t(`validation.field_names.${error.field}`),
        ...error.options
      };

      // Show the translated error message
      show(t(translationKey, translationOptions), { isError: true });
    } else {
      // Generic error message
      show(t("settings.notifications.error"), { isError: true });
    }

    errorCallback();
  };

  const onSaveBarSave = useCallback(() => {
    saveChanges(shopSettings, transactionSettings, () =>
      setSaveBarVisible(false)
    );
  }, [shopSettings, transactionSettings]);

  const onSaveBarDiscard = useCallback(() => {
    setShopSettings(shopSettingsData.shop_setting);
    setTransactionSettings(shopSettingsData.transaction_setting);
    setSaveBarVisible(false);
  }, [shopSettingsData]);

  const renderTabComponent = () => {
    switch (selected) {
      case 0:
        return (
          <GeneralSettings
            shopSettings={shopSettings}
            beaconMessages={domainsMessages[domainNames[selected]]}
            onSettingsChange={handleSettingsChange}
          />
        );
      case 1:
        return (
          <InvoiceSettings
            shopSettings={shopSettings}
            beaconMessages={domainsMessages[domainNames[selected]]}
            onSettingsChange={handleSettingsChange}
          />
        );
      case 2:
        return (
          <CreditNoteSettings
            shopSettings={shopSettings}
            beaconMessages={domainsMessages[domainNames[selected]]}
            onSettingsChange={handleSettingsChange}
          />
        );
      case 3:
        return (
          <PaymentDataSettings
            shopSettings={shopSettings}
            beaconMessages={domainsMessages[domainNames[selected]]}
            transactionSettings={transactionSettings}
            onSettingsChange={handleSettingsChange}
          />
        );
      case 4:
        return (
            <MailingSettings shopSettings={shopSettings} onSettingsChange={handleSettingsChange} />
        );
      default:
        return null;
    }
  };

  const tabs = [
    {
      id: "general",
      content: t("settings.general.tab_title"),
      accessibilityLabel: "General",
      panelID: "general-settings",
    },
    {
      id: "invoices",
      content: t("settings.invoices.tab_title"),
      accessibilityLabel: "Invoices",
      panelID: "invoices-settings",
    },
    {
      id: "credit-notes",
      content: t("settings.refunds.tab_title"),
      accessibilityLabel: "Credit Notes",
      panelID: "credit-notes-settings",
    },
    {
      id: "payment-data",
      content: t("settings.payment_data.tab_title"),
      accessibilityLabel: "Payment Data",
      panelID: "payment-data-settings",
    },
    {
      id: 'mailing',
      content: t('settings.mailing.tab_title'),
      accessibilityLabel: 'Mailing',
      panelID: 'mailing-settings',
    }
  ];

  return (
    <Page
      title={t("settings.page_title")}
      subtitle={t("general.app_name")}
      backAction={{ content: "Back", onAction: () => navigate(-1) }}
    >
      <Divider />
      <Frame>
        <OrderDataProvider>
          <ViewModeProvider>
            <Page>
              {isLoadingSettings && <Loading />}
              {!isLoadingSettings && shopInfo && (
                <>
                  <SaveBar
                    visible={saveBarVisible}
                    setVisible={setSaveBarVisible}
                    onSave={onSaveBarSave}
                    onDiscard={onSaveBarDiscard}
                  />

                  <InlineStack
                    gap="200"
                    align="space-between"
                    blockAlign="center"
                  >
                    <Tabs
                      tabs={tabs}
                      selected={selected}
                      onSelect={handleTabChange}
                    />
                    <ViewModeSelection />
                  </InlineStack>

                  <div>
                    <PlanActivationWarnings shopStatus={shopInfo} />

                    {renderTabComponent()}
                  </div>
                </>
              )}
            </Page>
          </ViewModeProvider>
        </OrderDataProvider>
      </Frame>
      <HelpFooter />
    </Page>
  );
}
